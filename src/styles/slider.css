/* Custom Range Slider Styles */
.slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider-thumb::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #02164F;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider-thumb::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #02164F;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider-thumb::-webkit-slider-track {
  background: transparent;
}

.slider-thumb::-moz-range-track {
  background: transparent;
}

.slider-thumb:focus {
  outline: none;
}

.slider-thumb:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(2, 22, 79, 0.2);
}

.slider-thumb:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(2, 22, 79, 0.2);
}
